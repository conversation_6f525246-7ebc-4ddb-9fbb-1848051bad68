<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>航点样式演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: #f0f2f5;
            margin: 0;
            padding: 20px;
        }

        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        .waypoint-samples {
            display: flex;
            justify-content: space-around;
            align-items: flex-end;
            background: #fafafa;
            padding: 60px 20px 40px;
            border-radius: 8px;
            margin-bottom: 30px;
            min-height: 300px;
        }

        .sample-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .sample-label {
            font-size: 14px;
            color: #666;
            font-weight: bold;
            background: white;
            padding: 5px 10px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        /* 航点样式 */
        .waypoint-marker-3d {
            position: relative;
            cursor: pointer;
            width: 40px;
            height: auto;
            transform-style: preserve-3d;
            transition: all 0.3s ease;
        }

        .waypoint-marker-3d:hover {
            transform: scale(1.05);
        }

        .waypoint-3d-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            transform-style: preserve-3d;
        }

        .height-label {
            position: absolute;
            top: -25px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: #ffffff;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
            white-space: nowrap;
            pointer-events: none;
            z-index: 1000;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .waypoint-circle-main {
            position: relative;
            width: 36px;
            height: 36px;
            margin-bottom: 2px;
            z-index: 100;
        }

        .waypoint-circle-inner {
            width: 36px;
            height: 36px;
            background: radial-gradient(circle at 30% 30%, #52c41a, #389e0d);
            border: 3px solid #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 
                0 4px 12px rgba(56, 158, 13, 0.4),
                inset 0 2px 4px rgba(255, 255, 255, 0.3);
            position: relative;
            transition: all 0.3s ease;
        }

        .waypoint-circle-inner:hover {
            transform: scale(1.1);
            box-shadow: 
                0 6px 16px rgba(56, 158, 13, 0.5),
                inset 0 2px 4px rgba(255, 255, 255, 0.4);
        }

        .waypoint-number {
            color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            pointer-events: none;
        }

        .height-line {
            position: relative;
            width: 3px;
            background: transparent;
            margin: 0 auto;
            z-index: 50;
            transition: height 0.2s ease-out;
        }

        .height-line-inner {
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, 
                rgba(82, 196, 26, 0.8) 0%, 
                rgba(82, 196, 26, 0.6) 50%, 
                rgba(82, 196, 26, 0.3) 100%
            );
            border-radius: 1.5px;
            box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);
            position: relative;
            transition: all 0.3s ease;
        }

        .height-line-inner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            width: 1px;
            height: 100%;
            background: rgba(255, 255, 255, 0.3);
            transform: translateX(-50%);
        }

        .ground-point {
            width: 8px;
            height: 8px;
            background: rgba(82, 196, 26, 0.6);
            border: 1px solid rgba(82, 196, 26, 0.8);
            border-radius: 50%;
            margin: 0 auto;
            box-shadow: 0 0 4px rgba(82, 196, 26, 0.3);
            transition: all 0.3s ease;
        }

        .ground-point:hover {
            transform: scale(1.2);
            background: rgba(82, 196, 26, 0.8);
        }

        .instructions {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #52c41a;
        }

        .instructions h3 {
            margin-top: 0;
            color: #333;
            font-size: 18px;
        }

        .instructions ul {
            margin: 15px 0;
            padding-left: 25px;
        }

        .instructions li {
            margin: 10px 0;
            color: #555;
            line-height: 1.6;
        }

        .feature-highlight {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
        }

        .feature-highlight h4 {
            margin: 0 0 10px 0;
            color: #1890ff;
        }

        .feature-highlight p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🎯 航点样式设计演示</h1>
        
        <div class="waypoint-samples">
            <!-- 不同高度的航点演示 -->
            <div class="sample-item">
                <div class="waypoint-marker-3d">
                    <div class="waypoint-3d-container">
                        <div class="height-label">30m</div>
                        <div class="waypoint-circle-main">
                            <div class="waypoint-circle-inner">
                                <span class="waypoint-number">1</span>
                            </div>
                        </div>
                        <div class="height-line" style="height: 24px;">
                            <div class="height-line-inner"></div>
                        </div>
                        <div class="ground-point"></div>
                    </div>
                </div>
                <div class="sample-label">高度: 30m</div>
            </div>

            <div class="sample-item">
                <div class="waypoint-marker-3d">
                    <div class="waypoint-3d-container">
                        <div class="height-label">60m</div>
                        <div class="waypoint-circle-main">
                            <div class="waypoint-circle-inner">
                                <span class="waypoint-number">2</span>
                            </div>
                        </div>
                        <div class="height-line" style="height: 48px;">
                            <div class="height-line-inner"></div>
                        </div>
                        <div class="ground-point"></div>
                    </div>
                </div>
                <div class="sample-label">高度: 60m</div>
            </div>

            <div class="sample-item">
                <div class="waypoint-marker-3d">
                    <div class="waypoint-3d-container">
                        <div class="height-label">100m</div>
                        <div class="waypoint-circle-main">
                            <div class="waypoint-circle-inner">
                                <span class="waypoint-number">3</span>
                            </div>
                        </div>
                        <div class="height-line" style="height: 80px;">
                            <div class="height-line-inner"></div>
                        </div>
                        <div class="ground-point"></div>
                    </div>
                </div>
                <div class="sample-label">高度: 100m</div>
            </div>

            <div class="sample-item">
                <div class="waypoint-marker-3d">
                    <div class="waypoint-3d-container">
                        <div class="height-label">150m</div>
                        <div class="waypoint-circle-main">
                            <div class="waypoint-circle-inner">
                                <span class="waypoint-number">4</span>
                            </div>
                        </div>
                        <div class="height-line" style="height: 120px;">
                            <div class="height-line-inner"></div>
                        </div>
                        <div class="ground-point"></div>
                    </div>
                </div>
                <div class="sample-label">高度: 150m</div>
            </div>

            <div class="sample-item">
                <div class="waypoint-marker-3d">
                    <div class="waypoint-3d-container">
                        <div class="height-label">200m</div>
                        <div class="waypoint-circle-main">
                            <div class="waypoint-circle-inner">
                                <span class="waypoint-number">5</span>
                            </div>
                        </div>
                        <div class="height-line" style="height: 160px;">
                            <div class="height-line-inner"></div>
                        </div>
                        <div class="ground-point"></div>
                    </div>
                </div>
                <div class="sample-label">高度: 200m</div>
            </div>
        </div>

        <div class="instructions">
            <h3>🎮 操作说明</h3>
            <ul>
                <li>🖱️ <strong>普通拖拽</strong>：移动航点的经纬度位置</li>
                <li>⌨️ <strong>Ctrl + 拖拽</strong>：只调整航点高度（上下移动）</li>
                <li>📏 <strong>高度线</strong>：垂直线长度根据实际高度按比例显示</li>
                <li>🎯 <strong>地面投影点</strong>：显示航点在地面的投影位置</li>
                <li>🏷️ <strong>高度标签</strong>：实时显示当前航点的高度值</li>
            </ul>

            <div class="feature-highlight">
                <h4>✨ 设计特点</h4>
                <p>
                    航点采用绿色圆圈设计，配有垂直向下的高度指示线和地面投影点。
                    高度线的长度与实际高度成正比，提供直观的高度感知。
                    按住Ctrl键拖拽时，航点只能上下移动来调整高度，确保精确的高度控制。
                </p>
            </div>
        </div>
    </div>
</body>
</html>
