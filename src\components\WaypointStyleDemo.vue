<template>
  <div class="waypoint-demo">
    <h3>航点样式演示</h3>
    <div class="demo-container">
      <!-- 演示不同高度的航点 -->
      <div class="waypoint-samples">
        <div class="sample-item" v-for="(height, index) in sampleHeights" :key="index">
          <div class="waypoint-marker-3d">
            <div class="waypoint-3d-container">
              <!-- 高度标签 -->
              <div class="height-label">{{ height }}m</div>
              
              <!-- 航点圆圈 -->
              <div class="waypoint-circle-main">
                <div class="waypoint-circle-inner">
                  <span class="waypoint-number">{{ index + 1 }}</span>
                </div>
              </div>
              
              <!-- 垂直高度线 -->
              <div class="height-line" :style="{ height: Math.max(20, height * 0.8) + 'px' }">
                <div class="height-line-inner"></div>
              </div>
              
              <!-- 地面投影点 -->
              <div class="ground-point"></div>
            </div>
          </div>
          <div class="sample-label">高度: {{ height }}m</div>
        </div>
      </div>
      
      <!-- 操作说明 -->
      <div class="instructions">
        <h4>操作说明：</h4>
        <ul>
          <li>🖱️ 普通拖拽：移动航点位置</li>
          <li>⌨️ Ctrl + 拖拽：只调整航点高度（上下移动）</li>
          <li>📏 高度线长度根据实际高度按比例显示</li>
          <li>🎯 地面投影点显示航点在地面的位置</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WaypointStyleDemo',
  data() {
    return {
      sampleHeights: [30, 60, 100, 150, 200]
    }
  },
  mounted() {
    this.applyStyles()
  },
  methods: {
    applyStyles() {
      // 应用航点样式
      this.$nextTick(() => {
        const markers = this.$el.querySelectorAll('.waypoint-marker-3d')
        markers.forEach((marker, index) => {
          this.applyWaypointStyles(marker, this.sampleHeights[index])
        })
      })
    },
    
    applyWaypointStyles(markerContent, height) {
      markerContent.style.cssText = `
        position: relative;
        cursor: pointer;
        width: 40px;
        height: auto;
        transform-style: preserve-3d;
        transition: all 0.3s ease;
        margin: 20px;
      `

      // 3D容器样式
      const container = markerContent.querySelector('.waypoint-3d-container')
      if (container) {
        container.style.cssText = `
          position: relative;
          display: flex;
          flex-direction: column;
          align-items: center;
          transform-style: preserve-3d;
        `
      }

      // 高度标签样式
      const heightLabel = markerContent.querySelector('.height-label')
      if (heightLabel) {
        heightLabel.style.cssText = `
          position: absolute;
          top: -25px;
          left: 50%;
          transform: translateX(-50%);
          background: rgba(0, 0, 0, 0.8);
          color: #ffffff;
          padding: 3px 8px;
          border-radius: 4px;
          font-size: 11px;
          font-weight: bold;
          white-space: nowrap;
          pointer-events: none;
          z-index: 1000;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
          border: 1px solid rgba(255, 255, 255, 0.2);
        `
      }

      // 主航点圆圈容器
      const circleMain = markerContent.querySelector('.waypoint-circle-main')
      if (circleMain) {
        circleMain.style.cssText = `
          position: relative;
          width: 36px;
          height: 36px;
          margin-bottom: 2px;
          z-index: 100;
        `
      }

      // 航点圆圈内部
      const circleInner = markerContent.querySelector('.waypoint-circle-inner')
      if (circleInner) {
        circleInner.style.cssText = `
          width: 36px;
          height: 36px;
          background: radial-gradient(circle at 30% 30%, #52c41a, #389e0d);
          border: 3px solid #ffffff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 
            0 4px 12px rgba(56, 158, 13, 0.4),
            inset 0 2px 4px rgba(255, 255, 255, 0.3);
          position: relative;
          transition: all 0.3s ease;
        `
      }

      // 垂直高度线
      const heightLine = markerContent.querySelector('.height-line')
      if (heightLine) {
        heightLine.style.cssText = `
          position: relative;
          width: 3px;
          background: transparent;
          margin: 0 auto;
          z-index: 50;
        `
      }

      // 高度线内部
      const heightLineInner = markerContent.querySelector('.height-line-inner')
      if (heightLineInner) {
        heightLineInner.style.cssText = `
          width: 100%;
          height: 100%;
          background: linear-gradient(to bottom, 
            rgba(82, 196, 26, 0.8) 0%, 
            rgba(82, 196, 26, 0.6) 50%, 
            rgba(82, 196, 26, 0.3) 100%
          );
          border-radius: 1.5px;
          box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);
          position: relative;
        `
      }

      // 地面投影点
      const groundPoint = markerContent.querySelector('.ground-point')
      if (groundPoint) {
        groundPoint.style.cssText = `
          width: 8px;
          height: 8px;
          background: rgba(82, 196, 26, 0.6);
          border: 1px solid rgba(82, 196, 26, 0.8);
          border-radius: 50%;
          margin: 0 auto;
          box-shadow: 0 0 4px rgba(82, 196, 26, 0.3);
        `
      }

      // 数字样式
      const numberSpan = markerContent.querySelector('.waypoint-number')
      if (numberSpan) {
        numberSpan.style.cssText = `
          color: #ffffff;
          font-size: 14px;
          font-weight: bold;
          line-height: 1;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          pointer-events: none;
        `
      }

      // 添加悬停效果
      markerContent.addEventListener('mouseenter', () => {
        const circleInner = markerContent.querySelector('.waypoint-circle-inner')
        if (circleInner) {
          circleInner.style.transform = 'scale(1.1)'
          circleInner.style.boxShadow = `
            0 6px 16px rgba(56, 158, 13, 0.5),
            inset 0 2px 4px rgba(255, 255, 255, 0.4)
          `
        }
      })

      markerContent.addEventListener('mouseleave', () => {
        const circleInner = markerContent.querySelector('.waypoint-circle-inner')
        if (circleInner) {
          circleInner.style.transform = 'scale(1)'
          circleInner.style.boxShadow = `
            0 4px 12px rgba(56, 158, 13, 0.4),
            inset 0 2px 4px rgba(255, 255, 255, 0.3)
          `
        }
      })
    }
  }
}
</script>

<style scoped>
.waypoint-demo {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  margin: 20px;
}

.demo-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.waypoint-samples {
  display: flex;
  justify-content: space-around;
  align-items: flex-end;
  background: #ffffff;
  padding: 40px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sample-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.sample-label {
  font-size: 12px;
  color: #666;
  font-weight: bold;
}

.instructions {
  background: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.instructions h4 {
  margin-top: 0;
  color: #333;
}

.instructions ul {
  margin: 10px 0;
  padding-left: 20px;
}

.instructions li {
  margin: 8px 0;
  color: #555;
}
</style>
