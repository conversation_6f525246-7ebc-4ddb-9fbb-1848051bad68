<template>
  <div class="map-container">
    <div id="map" class="map"></div>
  </div>
</template>

<script>
import mapMixin from '@/views/flightmaster/mixins/mapMixin.js'
export default {
    mixins: [ mapMixin],
  name: 'Amap',
  props: {
    coordinates: {
      type: Array,
      default: () => []
    },
    zoom: {
      type: Number,
      default: 13
    },
    viewMode: {
      type: String,
      default: '2D' // '2D' 或 '3D'
    },
    pitch: {
      type: Number,
      default: 0 // 俯仰角度，3D模式下有效
    },
    rotation: {
      type: Number,
      default: 0 // 旋转角度
    }
  },
  data() {
    return {
      map: null
    }
  },
  mounted() {
    this.initMap()
  },
  methods: {
    // API 2.0 地图初始化
    async initMap() {
      try {
        const wgs84Coord = this.gcj02ToWgs84(113.014701, 28.194985);

        // API 2.0 地图配置
        this.map = new AMap.Map('map', {
          center: this.coordinates[0] || [wgs84Coord.lng, wgs84Coord.lat],
          zoom: this.zoom,
          viewMode: this.viewMode,
          pitch: this.pitch, // 俯仰角度，3D模式下有效
          rotation: this.rotation, // 旋转角度
          // API 2.0 新增配置
          mapStyle: 'amap://styles/normal',
          features: ['bg', 'point', 'road', 'building'],
          showLabel: true,
          defaultCursor: 'pointer',
          animateEnable: true,
          jogEnable: true,
          pitchEnable: this.viewMode === '3D', // 3D模式下启用俯仰
          rotateEnable: this.viewMode === '3D', // 3D模式下启用旋转
          // 性能优化配置
          resizeEnable: true,
          showIndoorMap: false,
          expandZoomRange: true,
          zooms: [3, 20]
        });

        // 地图加载完成事件
        this.map.on('complete', () => {
          console.log('地图加载完成 (API 2.0)');
          this.initGeolocation();
        });

        // 地图点击事件
        this.map.on('click', (e) => {
          console.log('地图点击:', e.lnglat);
          this.$emit('map-click', e.lnglat);
        });

        // 地图缩放事件
        this.map.on('zoomend', () => {
          const zoom = this.map.getZoom();
          console.log('地图缩放:', zoom);
          this.$emit('zoom-change', zoom);
        });

      } catch (error) {
        console.error('地图初始化失败:', error);
      }
    },

    // API 2.0 定位功能
    async initGeolocation() {
      try {
        // 使用 API 2.0 的异步插件加载
        await new Promise((resolve) => {
          AMap.plugin(['AMap.Geolocation'], resolve);
        });

        const geolocation = new AMap.Geolocation({
          // API 2.0 优化配置
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 60000,
          convert: true,
          showMarker: true,
          showCircle: true,
          panToLocation: false, // 不自动移动到定位位置
          zoomToAccuracy: false,
          // API 2.0 新增配置
          markerOptions: {
            offset: new AMap.Pixel(-18, -36),
            content: '<div class="geolocation-marker"></div>'
          },
          circleOptions: {
            fillColor: '#1890ff',
            fillOpacity: 0.2,
            strokeColor: '#1890ff',
            strokeOpacity: 0.5,
            strokeWeight: 2
          }
        });

        // 添加定位控件
        this.map.addControl(geolocation);

        // 定位回调
        geolocation.getCurrentPosition((status, result) => {
          if (status === 'complete') {
            console.log('定位成功 (API 2.0):', result);
            this.$emit('location-success', result);
          } else {
            console.warn('定位失败 (API 2.0):', result);
            this.$emit('location-error', result);
          }
        });

      } catch (error) {
        console.error('定位功能初始化失败:', error);
      }
    }
  }
}
</script>

<style>
.map-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

#map {
  width: 100%;
  height: 100%;
}

/* API 2.0 定位标记样式 */
.geolocation-marker {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #1890ff, #096dd9);
  border: 3px solid white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
}

.geolocation-marker::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
}

/* API 2.0 地图控件样式优化 */
.amap-controls {
  z-index: 1000;
}

.amap-geolocation-con {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15) !important;
}
</style>